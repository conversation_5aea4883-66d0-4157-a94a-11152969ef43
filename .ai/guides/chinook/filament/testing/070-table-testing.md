# Table Testing Guide

This guide covers comprehensive testing strategies for Filament table functionality in the Chinook admin panel, including data display, filtering, searching, sorting, and bulk operations.

## Table of Contents

- [Overview](#overview)
- [Basic Table Testing](#basic-table-testing)
- [Data Display Testing](#data-display-testing)
- [Filtering and Search Testing](#filtering-and-search-testing)
- [Sorting and Pagination Testing](#sorting-and-pagination-testing)
- [Column Configuration Testing](#column-configuration-testing)
- [Bulk Operations Testing](#bulk-operations-testing)
- [Performance Testing](#performance-testing)
- [Accessibility Testing](#accessibility-testing)

## Overview

Table testing ensures that Filament tables display data correctly, provide effective filtering and search capabilities, and maintain good performance with large datasets. This includes testing all table features and user interactions.

### Testing Objectives

- **Data Accuracy**: Verify correct data display and formatting
- **Functionality**: Test filtering, searching, and sorting capabilities
- **Performance**: Validate table performance with large datasets
- **User Experience**: Test pagination, bulk operations, and interactions
- **Accessibility**: Ensure WCAG 2.1 AA compliance for table elements

## Basic Table Testing

### Table Structure Testing

```php
<?php

namespace Tests\Feature\ChinookAdmin\Tables;

use App\Models\Artist;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ArtistTableTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create();
        $this->adminUser->assignRole('Admin');
    }

    public function test_table_displays_correct_columns(): void
    {
        $artist = Artist::factory()->create([
            'name' => 'Test Artist',
            'country' => 'US',
            'formed_year' => 2020,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get('/chinook-admin/artists');

        $response->assertStatus(200)
            ->assertSee($artist->name)
            ->assertSee($artist->country)
            ->assertSee('2020')
            ->assertSee('Active');
    }

    public function test_table_shows_empty_state(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->get('/chinook-admin/artists');

        $response->assertStatus(200)
            ->assertSee('No artists found');
    }

    public function test_table_respects_per_page_setting(): void
    {
        Artist::factory()->count(30)->create();

        $response = $this->actingAs($this->adminUser)
            ->get('/chinook-admin/artists?per_page=10');

        $response->assertStatus(200);
        
        // Should show pagination controls
        $response->assertSee('Next')
            ->assertSee('Previous');
    }
}
```

### Column Display Testing

```php
public function test_column_formatting(): void
{
    $artist = Artist::factory()->create([
        'name' => 'Test Artist',
        'website' => 'https://example.com',
        'formed_year' => 2020,
        'is_active' => true,
    ]);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists');

    $response->assertStatus(200)
        ->assertSee($artist->name)
        ->assertSee('https://example.com')
        ->assertSee('2020')
        ->assertSee('Active'); // Boolean formatted as text
}

public function test_relationship_column_display(): void
{
    $artist = Artist::factory()->create(['name' => 'Test Artist']);
    $album = Album::factory()->for($artist)->create(['title' => 'Test Album']);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/albums');

    $response->assertStatus(200)
        ->assertSee('Test Album')
        ->assertSee('Test Artist'); // Artist name displayed in album table
}

public function test_custom_column_content(): void
{
    $artist = Artist::factory()->create();
    $albums = Album::factory()->count(3)->for($artist)->create();

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists');

    $response->assertStatus(200)
        ->assertSee('3 albums'); // Custom albums count column
}
```

## Data Display Testing

### Data Integrity Testing

```php
public function test_soft_deleted_records_hidden_by_default(): void
{
    $activeArtist = Artist::factory()->create(['name' => 'Active Artist']);
    $deletedArtist = Artist::factory()->create(['name' => 'Deleted Artist']);
    $deletedArtist->delete();

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists');

    $response->assertStatus(200)
        ->assertSee('Active Artist')
        ->assertDontSee('Deleted Artist');
}

public function test_inactive_records_display_correctly(): void
{
    $activeArtist = Artist::factory()->create([
        'name' => 'Active Artist',
        'is_active' => true,
    ]);
    
    $inactiveArtist = Artist::factory()->create([
        'name' => 'Inactive Artist',
        'is_active' => false,
    ]);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists');

    $response->assertStatus(200)
        ->assertSee('Active Artist')
        ->assertSee('Inactive Artist')
        ->assertSee('Active')
        ->assertSee('Inactive');
}

public function test_date_formatting(): void
{
    $artist = Artist::factory()->create([
        'created_at' => '2023-01-15 10:30:00',
    ]);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists');

    $response->assertStatus(200)
        ->assertSee('Jan 15, 2023'); // Formatted date
}
```

### Badge and Status Testing

```php
public function test_status_badges_display(): void
{
    $activeArtist = Artist::factory()->create(['is_active' => true]);
    $inactiveArtist = Artist::factory()->create(['is_active' => false]);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists');

    $response->assertStatus(200)
        ->assertSee('badge-success') // Active badge class
        ->assertSee('badge-danger'); // Inactive badge class
}

public function test_category_badges(): void
{
    $artist = Artist::factory()->create();
    $genres = Category::factory()->count(2)->genre()->create();
    $artist->attachCategories($genres->pluck('id')->toArray());

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists');

    $response->assertStatus(200);
    
    foreach ($genres as $genre) {
        $response->assertSee($genre->name);
    }
}
```

## Filtering and Search Testing

### Global Search Testing

```php
public function test_global_search_functionality(): void
{
    $artist1 = Artist::factory()->create(['name' => 'Searchable Artist']);
    $artist2 = Artist::factory()->create(['name' => 'Another Band']);
    $artist3 = Artist::factory()->create(['name' => 'Different Group']);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?search=Searchable');

    $response->assertStatus(200)
        ->assertSee('Searchable Artist')
        ->assertDontSee('Another Band')
        ->assertDontSee('Different Group');
}

public function test_search_across_multiple_fields(): void
{
    $artist1 = Artist::factory()->create([
        'name' => 'Test Artist',
        'country' => 'US',
    ]);
    
    $artist2 = Artist::factory()->create([
        'name' => 'Another Artist',
        'country' => 'Canada',
    ]);

    // Search by name
    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?search=Test');

    $response->assertStatus(200)
        ->assertSee('Test Artist')
        ->assertDontSee('Another Artist');

    // Search by country
    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?search=Canada');

    $response->assertStatus(200)
        ->assertSee('Another Artist')
        ->assertDontSee('Test Artist');
}
```

### Filter Testing

```php
public function test_status_filter(): void
{
    $activeArtist = Artist::factory()->create(['is_active' => true]);
    $inactiveArtist = Artist::factory()->create(['is_active' => false]);

    // Filter for active only
    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?filter[is_active]=1');

    $response->assertStatus(200)
        ->assertSee($activeArtist->name)
        ->assertDontSee($inactiveArtist->name);

    // Filter for inactive only
    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?filter[is_active]=0');

    $response->assertStatus(200)
        ->assertSee($inactiveArtist->name)
        ->assertDontSee($activeArtist->name);
}

public function test_country_filter(): void
{
    $usArtist = Artist::factory()->create(['country' => 'US']);
    $caArtist = Artist::factory()->create(['country' => 'CA']);
    $gbArtist = Artist::factory()->create(['country' => 'GB']);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?filter[country]=US');

    $response->assertStatus(200)
        ->assertSee($usArtist->name)
        ->assertDontSee($caArtist->name)
        ->assertDontSee($gbArtist->name);
}

public function test_date_range_filter(): void
{
    $oldArtist = Artist::factory()->create(['formed_year' => 1980]);
    $newArtist = Artist::factory()->create(['formed_year' => 2020]);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?filter[formed_year_from]=2000');

    $response->assertStatus(200)
        ->assertSee($newArtist->name)
        ->assertDontSee($oldArtist->name);
}
```

### Advanced Filter Testing

```php
public function test_category_filter(): void
{
    $rockGenre = Category::factory()->genre()->create(['name' => 'Rock']);
    $jazzGenre = Category::factory()->genre()->create(['name' => 'Jazz']);
    
    $rockArtist = Artist::factory()->create();
    $rockArtist->attachCategories([$rockGenre->id]);
    
    $jazzArtist = Artist::factory()->create();
    $jazzArtist->attachCategories([$jazzGenre->id]);

    $response = $this->actingAs($this->adminUser)
        ->get("/chinook-admin/artists?filter[categories]={$rockGenre->id}");

    $response->assertStatus(200)
        ->assertSee($rockArtist->name)
        ->assertDontSee($jazzArtist->name);
}

public function test_multiple_filters_combination(): void
{
    $usRockArtist = Artist::factory()->create(['country' => 'US', 'is_active' => true]);
    $usRockGenre = Category::factory()->genre()->create(['name' => 'Rock']);
    $usRockArtist->attachCategories([$usRockGenre->id]);
    
    $caJazzArtist = Artist::factory()->create(['country' => 'CA', 'is_active' => true]);
    $jazzGenre = Category::factory()->genre()->create(['name' => 'Jazz']);
    $caJazzArtist->attachCategories([$jazzGenre->id]);

    $response = $this->actingAs($this->adminUser)
        ->get("/chinook-admin/artists?filter[country]=US&filter[categories]={$usRockGenre->id}&filter[is_active]=1");

    $response->assertStatus(200)
        ->assertSee($usRockArtist->name)
        ->assertDontSee($caJazzArtist->name);
}
```

## Sorting and Pagination Testing

### Sorting Testing

```php
public function test_name_sorting(): void
{
    Artist::factory()->create(['name' => 'Zebra Band']);
    Artist::factory()->create(['name' => 'Alpha Group']);
    Artist::factory()->create(['name' => 'Beta Artists']);

    // Test ascending sort
    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?sort=name&direction=asc');

    $content = $response->getContent();
    $alphaPos = strpos($content, 'Alpha Group');
    $betaPos = strpos($content, 'Beta Artists');
    $zebraPos = strpos($content, 'Zebra Band');

    $this->assertTrue($alphaPos < $betaPos);
    $this->assertTrue($betaPos < $zebraPos);

    // Test descending sort
    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?sort=name&direction=desc');

    $content = $response->getContent();
    $alphaPos = strpos($content, 'Alpha Group');
    $betaPos = strpos($content, 'Beta Artists');
    $zebraPos = strpos($content, 'Zebra Band');

    $this->assertTrue($zebraPos < $betaPos);
    $this->assertTrue($betaPos < $alphaPos);
}

public function test_date_sorting(): void
{
    Artist::factory()->create(['formed_year' => 2020, 'name' => 'New Band']);
    Artist::factory()->create(['formed_year' => 1980, 'name' => 'Old Band']);
    Artist::factory()->create(['formed_year' => 2000, 'name' => 'Middle Band']);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?sort=formed_year&direction=asc');

    $content = $response->getContent();
    $oldPos = strpos($content, 'Old Band');
    $middlePos = strpos($content, 'Middle Band');
    $newPos = strpos($content, 'New Band');

    $this->assertTrue($oldPos < $middlePos);
    $this->assertTrue($middlePos < $newPos);
}
```

### Pagination Testing

```php
public function test_pagination_functionality(): void
{
    Artist::factory()->count(25)->create();

    // Test first page
    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?page=1&per_page=10');

    $response->assertStatus(200)
        ->assertSee('Next')
        ->assertDontSee('Previous');

    // Test middle page
    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?page=2&per_page=10');

    $response->assertStatus(200)
        ->assertSee('Next')
        ->assertSee('Previous');

    // Test last page
    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?page=3&per_page=10');

    $response->assertStatus(200)
        ->assertDontSee('Next')
        ->assertSee('Previous');
}

public function test_per_page_options(): void
{
    Artist::factory()->count(50)->create();

    $perPageOptions = [10, 25, 50];

    foreach ($perPageOptions as $perPage) {
        $response = $this->actingAs($this->adminUser)
            ->get("/chinook-admin/artists?per_page={$perPage}");

        $response->assertStatus(200);
        
        if ($perPage < 50) {
            $response->assertSee('Next');
        }
    }
}
```

## Performance Testing

### Large Dataset Testing

```php
public function test_table_performance_with_large_dataset(): void
{
    Artist::factory()->count(1000)->create();

    $startTime = microtime(true);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists');

    $endTime = microtime(true);
    $loadTime = $endTime - $startTime;

    $response->assertStatus(200);
    $this->assertLessThan(2.0, $loadTime, "Table took {$loadTime} seconds to load");
}

public function test_search_performance(): void
{
    Artist::factory()->count(500)->create();

    $startTime = microtime(true);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?search=test');

    $endTime = microtime(true);
    $searchTime = $endTime - $startTime;

    $response->assertStatus(200);
    $this->assertLessThan(1.0, $searchTime, "Search took {$searchTime} seconds");
}

public function test_filter_performance(): void
{
    Artist::factory()->count(500)->create();

    $startTime = microtime(true);

    $response = $this->actingAs($this->adminUser)
        ->get('/chinook-admin/artists?filter[is_active]=1');

    $endTime = microtime(true);
    $filterTime = $endTime - $startTime;

    $response->assertStatus(200);
    $this->assertLessThan(1.0, $filterTime, "Filter took {$filterTime} seconds");
}
```

## Related Documentation

- **[Form Testing](060-form-testing.md)** - Form validation and component testing
- **[Action Testing](080-action-testing.md)** - Custom actions and bulk operations
- **[Performance Testing](130-performance-testing.md)** - Load testing and optimization
- **[Browser Testing](140-browser-testing.md)** - End-to-end table interaction testing

---

## Navigation

**← Previous:** [Form Testing](060-form-testing.md)

**Next →** [Action Testing](080-action-testing.md)

**Up:** [Testing Documentation Index](000-index.md)
